"""
sweeper400.analyze.my_dtypes - 自定义数据类型模块

本模块定义了sweeper400项目中特有的自定义数据类型和容器。
主要包含用于管理时域波形数据的Waveform类。
"""

import numpy as np
from typing import Optional, Union, Any, List, NewType, TypedDict
from sweeper400.logger import get_logger

# 获取模块日志器
logger = get_logger(__name__)


# 定义 "PositiveInt" 类型
PositiveInt = NewType("PositiveInt", int)


def validate_positive(num: int) -> PositiveInt:
    if num <= 0:
        raise ValueError("PositiveInt必须是正整数")
    return PositiveInt(num)


# 定义 "SamplingInfo" 类型
class SamplingInfo(TypedDict):
    sampling_rate: PositiveInt  # 正整数
    samples_num: PositiveInt  # 正整数


# 定义 "Waveform" 类型
class Waveform(np.ndarray):
    """
    时域波形数据容器类

    继承自numpy.ndarray，用于管理时域波形数据及其元数据。
    支持单通道数据（一维数组）和多通道数据（二维数组）。

    新增属性:
        sampling_rate: 波形数据的采样率（Hz），只读属性
        timestamp: 波形采样开始时间戳，可修改属性

    Examples:
        # 创建单通道波形
        data = np.array([1.0, 2.0, 3.0, 4.0])
        waveform = Waveform(data, sampling_rate=1000)

        # 创建多通道波形
        data = np.array([[1.0, 2.0, 3.0], [4.0, 5.0, 6.0]])
        waveform = Waveform(data, sampling_rate=1000)

        # 指定时间戳
        waveform = Waveform(data, sampling_rate=1000,
                           timestamp=np.datetime64("2024-01-01T12:00:00", "ns"))
    """

    def __new__(
        cls,
        input_array: Union[np.ndarray, List[float]],  # python原生float也即numpy.float64
        sampling_rate: PositiveInt,
        timestamp: Optional[np.datetime64] = None,
        **kwargs: Any,
    ) -> "Waveform":
        """
        创建Waveform对象

        Args:
            input_array: 输入的波形数据数组
            sampling_rate: 采样率（Hz），必须为正整数
            timestamp: 采样开始时间戳，可选，默认为当前时间
            **kwargs: 传递给numpy.ndarray的其他参数

        Returns:
            Waveform对象实例
        """
        # 转换输入数组为numpy数组
        try:
            obj = np.asarray(input_array, dtype=np.float64).view(cls)
        except Exception as e:
            logger.error(f"无法将输入数据转换为numpy数组: {e}")
            raise TypeError(f"输入数据无法转换为numpy数组: {e}")

        # 验证数组维度（只支持1D和2D）
        if obj.ndim not in [1, 2]:
            raise ValueError(f"只支持1维或2维数组，得到{obj.ndim}维数组")

        # 设置只读属性
        obj._sampling_rate = sampling_rate

        # 设置时间戳
        if timestamp is None:
            obj.timestamp = np.datetime64("now", "ns")
            logger.debug(f"使用当前时间作为时间戳: {obj.timestamp}")
        else:
            obj.timestamp = timestamp
            logger.debug(f"使用指定时间戳: {obj.timestamp}")

        logger.debug(
            f"创建Waveform对象: shape={obj.shape}, sampling_rate={sampling_rate}Hz"
        )

        return obj

    def __array_finalize__(self, obj: Optional[np.ndarray]) -> None:
        """
        数组完成时的回调函数

        在numpy数组操作后保持自定义属性
        """
        if obj is None:
            return

        # 保持属性
        self._sampling_rate = getattr(obj, "_sampling_rate")
        self.timestamp = getattr(obj, "timestamp")

    @property
    def sampling_rate(self) -> int:
        """
        采样率（Hz）

        只读属性，只能在对象创建时指定

        Returns:
            采样率值（Hz）
        """
        return self._sampling_rate

    @property
    def channels_num(self) -> int:
        """
        通道数

        Returns:
            通道数，单通道返回1，多通道返回通道数
        """
        if self.ndim == 1:
            return 1
        else:
            return self.shape[0]

    @property
    def samples_num(self) -> int:
        """
        采样点数

        Returns:
            每个通道的采样点数
        """
        if self.ndim == 1:
            return self.shape[0]
        else:
            return self.shape[1]

    @property
    def duration(self) -> float:
        """
        波形持续时间（秒）

        Returns:
            波形持续时间，单位为秒
        """
        return self.samples_num / self.sampling_rate

    def __repr__(self) -> str:
        """返回对象的字符串表示"""
        return (
            f"Waveform(shape={self.shape}, "
            f"sampling_rate={self.sampling_rate}Hz, "
            f"duration={self.duration:.6f}s, "
            f"timestamp={self.timestamp})"
        )

    def __str__(self) -> str:
        """返回对象的简洁字符串表示"""
        return self.__repr__()
