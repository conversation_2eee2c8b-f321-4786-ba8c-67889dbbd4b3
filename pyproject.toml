[tool.pyright]

# 明确指定 Python 版本（匹配你的实际环境）
pythonVersion = "3.12"

# 默认使用严格检查
typeCheckingMode = "strict"

# 排除某些包和文件夹的类型检查
exclude = [
    "nidaqmx",
    "参考资料/**",
    "tests/**"
]

# 禁止报告缺失的 Stub 文件警告（仅针对 nidaqmx）
reportMissingTypeStubs = "none"



[tool.black]

# 推荐稍宽的行宽（Black 默认88，但现代屏幕可适当放宽）
line-length = 120

# 明确指定Python版本，启用最新语法兼容
target-version = ["py312"]

# 强制统一引号风格（单引号优先）
skip-string-normalization = false

# 启用实验性风格改进（Black 23+版本支持）
preview = true