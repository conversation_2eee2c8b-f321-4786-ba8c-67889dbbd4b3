---
type: "manual"
---

<Moving_Rules>
以下Rules适用于步进电机控制相关功能（"pysweep\move"目录下）的开发。
- 我们正在使用python调用dll格式的API，来与一个MT-22E型号的机电控制器通讯。该控制器将会控制一个真实的两轴步进电机在物理世界中进行运动。
- 存储API的dll文件位于"pysweep\move\MT_API.dll"路径下。关于它的使用方式，请你参考"参考资料\API资料"文件夹中的内容。其中”二次开发手册“最为重要，它包含了所有API函数的详细说明。”参考例程“也很有用，它包含一个简单的python环境下正确调用API的例程文件。
- 我们的PC和电机控制器使用USB方式连接。我们的步进电机只有X和Y两个运动轴，Z轴并不存在，因此你无需尝试控制Z轴。如果你需要获知控制器和电机的具体参数，可以直接使用dll中的硬件相关API（详情请参见开发手册）。
- 我们的具体工作方式是，在"pysweep\move\move.py"这一模块中实现所有电机控制相关的函数/类/方法/属性，，并在根目录的"test"中创建测试文件，调用"move.py"模块，实现相关操作。
</Moving_Rules>