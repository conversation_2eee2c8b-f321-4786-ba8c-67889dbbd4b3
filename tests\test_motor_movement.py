"""
测试电机实际移动距离以确认细分数

该测试用于：
1. 使用更新的螺距参数（4mm）
2. 假设细分数为64
3. 让电机移动10mm
4. 通过实际测量结果确认细分数是否正确
"""

import sys
import os
import time

# 添加项目根目录到Python路径
project_root = os.path.join(os.path.dirname(__file__), "..")
sys.path.insert(0, project_root)

# 添加sweeper400包路径
sweeper400_path = os.path.join(project_root, "sweeper400")
sys.path.insert(0, sweeper400_path)

from sweeper400.move.move import MotorController
import logging

# 配置日志级别
logging.basicConfig(level=logging.INFO)

def test_motor_movement():
    """测试电机实际移动距离"""
    print("=" * 60)
    print("电机移动距离测试")
    print("=" * 60)
    
    controller = MotorController()
    
    try:
        # 1. 初始化和连接
        print("\n1. 初始化API和连接...")
        if not controller.initialize():
            print("× API初始化失败")
            return False
            
        if not controller.connect_usb():
            print("× USB连接失败")
            return False
        
        print("√ 初始化和连接成功")
        
        # 2. 设置电机参数（使用准确的螺距4mm和假设的细分数64）
        print("\n2. 设置电机参数...")
        step_angle = 1.8      # 步进角度（度）
        subdivision = 64      # 细分数（假设值，待验证）
        pitch = 4.0          # 螺距（mm）- 准确测量值
        transmission_ratio = 1.0  # 传动比
        
        controller.set_motor_parameters(step_angle, subdivision, pitch, transmission_ratio)
        
        # 显示计算的换算系数
        conversion_factor = controller.get_conversion_factor()
        print(f"√ 换算系数: {conversion_factor:.2f} 步/mm")
        
        # 3. 计算10mm对应的脉冲数
        target_distance = 10.0  # mm
        steps_needed = controller.mm_to_steps(target_distance)
        print(f"√ {target_distance}mm 对应 {steps_needed} 步")
        
        # 4. 设置电机运动参数
        print("\n3. 设置电机运动参数...")
        axis = 0  # X轴（第一轴）
        
        # 停止所有电机
        controller._api.MT_Set_Axis_Halt_All()
        
        # 设置位置模式
        controller._api.MT_Set_Axis_Mode_Position(axis)
        controller._api.MT_Set_Axis_Position_Acc(axis, 2000)    # 加速度
        controller._api.MT_Set_Axis_Position_Dec(axis, 2000)    # 减速度
        controller._api.MT_Set_Axis_Position_V_Max(axis, 3000)  # 最大速度
        
        print("√ 电机运动参数设置完成")
        
        # 5. 获取当前位置
        from ctypes import c_int32, pointer
        current_pos = c_int32(0)
        p_pos = pointer(current_pos)
        controller._api.MT_Get_Axis_Software_P_Now(axis, p_pos)
        initial_position = current_pos.value
        print(f"√ 当前位置: {initial_position} 步")
        
        # 6. 执行移动
        print(f"\n4. 开始移动 {target_distance}mm ({steps_needed} 步)...")
        print("请准备测量电机的实际移动距离！")
        
        # 确认开始移动
        response = input("按回车键开始移动电机...")
        
        # 相对移动
        controller._api.MT_Set_Axis_Position_P_Target_Rel(axis, steps_needed)
        
        # 等待移动完成
        print("电机正在移动...")
        run_status = c_int32(0)
        p_run = pointer(run_status)
        
        # 监控运动状态
        while True:
            controller._api.MT_Get_Axis_Status_Run(axis, p_run)
            if run_status.value == 0:  # 0表示停止
                break
            time.sleep(0.1)
        
        # 获取最终位置
        controller._api.MT_Get_Axis_Software_P_Now(axis, p_pos)
        final_position = current_pos.value
        actual_steps = final_position - initial_position
        
        print("√ 移动完成！")
        print(f"  初始位置: {initial_position} 步")
        print(f"  最终位置: {final_position} 步")
        print(f"  实际移动: {actual_steps} 步")
        print(f"  理论移动: {steps_needed} 步")
        
        # 7. 计算理论移动距离
        theoretical_distance = controller.steps_to_mm(actual_steps)
        print(f"  理论距离: {theoretical_distance:.3f} mm")
        
        print("\n" + "=" * 60)
        print("请测量电机的实际移动距离")
        print("=" * 60)
        
        # 获取用户测量的实际距离
        while True:
            try:
                actual_distance = float(input("请输入实际测量的移动距离（mm）: "))
                break
            except ValueError:
                print("请输入有效的数字")
        
        # 8. 分析结果
        print(f"\n分析结果:")
        print(f"  目标距离: {target_distance:.1f} mm")
        print(f"  实际距离: {actual_distance:.1f} mm")
        print(f"  理论距离: {theoretical_distance:.3f} mm")
        
        error_percent = abs(actual_distance - target_distance) / target_distance * 100
        print(f"  误差百分比: {error_percent:.1f}%")
        
        if error_percent < 5:  # 误差小于5%认为参数正确
            print("√ 细分数64可能是正确的！")
            print(f"√ 确认的换算系数: {conversion_factor:.2f} 步/mm")
        else:
            # 计算正确的细分数
            actual_conversion = steps_needed / actual_distance
            correct_subdivision = (actual_conversion * pitch) / (360.0 / step_angle)
            
            print(f"× 细分数64可能不正确")
            print(f"  实际换算系数约为: {actual_conversion:.2f} 步/mm")
            print(f"  推算的细分数约为: {correct_subdivision:.1f}")
            
            # 找到最接近的标准细分数
            standard_subdivisions = [2, 4, 8, 16, 32, 64, 128, 256]
            closest_subdivision = min(standard_subdivisions, 
                                    key=lambda x: abs(x - correct_subdivision))
            print(f"  建议尝试细分数: {closest_subdivision}")
        
        return True
        
    except Exception as e:
        print(f"\n× 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 清理资源
        print("\n清理资源...")
        controller.cleanup()
        print("√ 资源清理完成")


if __name__ == "__main__":
    print("电机移动距离测试")
    print("目的：通过实际移动10mm来确认细分数参数")
    print("\n参数设置:")
    print("- 螺距: 4.0mm (准确测量值)")
    print("- 细分数: 64 (假设值，待验证)")
    print("- 移动距离: 10.0mm")
    print("\n请确保:")
    print("1. 电机控制器已正确连接")
    print("2. 电机可以安全移动10mm")
    print("3. 准备好测量工具")
    
    response = input("\n是否开始测试？(y/n): ").lower().strip()
    
    if response == 'y' or response == 'yes':
        success = test_motor_movement()
        if success:
            print("\n测试完成！")
        else:
            print("\n测试失败，请检查硬件连接。")
    else:
        print("测试已取消。")
