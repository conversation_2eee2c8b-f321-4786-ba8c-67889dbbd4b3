"""
sweeper400.analyze.basic_sine - 基础正弦波处理模块

本模块包含与最简单的单频正弦波相关的类和函数。
"""

from sweeper400.analyze.my_dtypes import PositiveInt, SamplingInfo
from sweeper400.logger import get_logger

# 获取模块日志器
logger = get_logger(__name__)


def init_sampling_info(sampling_rate: PositiveInt, samples_num: PositiveInt) -> SamplingInfo:
    """
    标准化地生成采样信息字典

    Args:
        sampling_rate: 采样率，必须为正整数（Hz）
        samples_num: 总采样数，必须为正整数

    Returns:
        sampling_info: 包含采样率和采样数信息的字典

    Examples:
        >>> sampling_info = init_sampling_info(PositiveInt(1000), PositiveInt(2048))
        >>> print(sampling_info)
        {'sampling_rate': 1000, 'samples_num': 2048}
    """
    logger.debug(f"创建采样信息: sampling_rate={sampling_rate}Hz, samples_num={samples_num}")

    sampling_info: SamplingInfo = {"sampling_rate": sampling_rate, "samples_num": samples_num}

    return sampling_info
