"""
测试Waveform类的功能
"""

import numpy as np
import pytest
from sweeper400.analyze import Waveform


def test_waveform_creation_1d():
    """测试创建单通道波形"""
    data = np.array([1.0, 2.0, 3.0, 4.0])
    sampling_rate = 1000
    
    waveform = Waveform(data, sampling_rate=sampling_rate)
    
    # 验证基本属性
    assert waveform.sampling_rate == sampling_rate
    assert waveform.channels == 1
    assert waveform.samples == 4
    assert waveform.duration == 4.0 / 1000
    assert waveform.shape == (4,)
    assert np.array_equal(waveform, data)
    
    # 验证时间戳类型
    assert isinstance(waveform.timestamp, np.datetime64)
    
    print(f"单通道波形创建成功: {waveform}")


def test_waveform_creation_2d():
    """测试创建多通道波形"""
    data = np.array([[1.0, 2.0, 3.0], [4.0, 5.0, 6.0]])
    sampling_rate = 2000
    
    waveform = Waveform(data, sampling_rate=sampling_rate)
    
    # 验证基本属性
    assert waveform.sampling_rate == sampling_rate
    assert waveform.channels == 2
    assert waveform.samples == 3
    assert waveform.duration == 3.0 / 2000
    assert waveform.shape == (2, 3)
    assert np.array_equal(waveform, data)
    
    print(f"多通道波形创建成功: {waveform}")


def test_waveform_with_timestamp():
    """测试指定时间戳创建波形"""
    data = np.array([1.0, 2.0, 3.0])
    sampling_rate = 1000
    timestamp = np.datetime64("2024-01-01T12:00:00", "ns")
    
    waveform = Waveform(data, sampling_rate=sampling_rate, timestamp=timestamp)
    
    assert waveform.timestamp == timestamp
    print(f"指定时间戳波形创建成功: {waveform}")


def test_waveform_timestamp_modification():
    """测试时间戳修改"""
    data = np.array([1.0, 2.0, 3.0])
    sampling_rate = 1000
    
    waveform = Waveform(data, sampling_rate=sampling_rate)
    original_timestamp = waveform.timestamp
    
    # 修改时间戳
    new_timestamp = np.datetime64("2024-12-31T23:59:59", "ns")
    waveform.timestamp = new_timestamp
    
    assert waveform.timestamp == new_timestamp
    assert waveform.timestamp != original_timestamp
    print(f"时间戳修改成功: {waveform.timestamp}")


def test_waveform_sampling_rate_readonly():
    """测试采样率只读属性"""
    data = np.array([1.0, 2.0, 3.0])
    sampling_rate = 1000
    
    waveform = Waveform(data, sampling_rate=sampling_rate)
    
    # 尝试修改采样率应该失败
    with pytest.raises(AttributeError):
        waveform.sampling_rate = 2000
    
    print("采样率只读属性验证成功")


def test_waveform_invalid_sampling_rate():
    """测试无效采样率"""
    data = np.array([1.0, 2.0, 3.0])
    
    # 测试零采样率
    with pytest.raises(ValueError):
        Waveform(data, sampling_rate=0)
    
    # 测试负采样率
    with pytest.raises(ValueError):
        Waveform(data, sampling_rate=-1000)
    
    print("无效采样率验证成功")


def test_waveform_invalid_timestamp():
    """测试无效时间戳"""
    data = np.array([1.0, 2.0, 3.0])
    sampling_rate = 1000
    
    # 测试非datetime64类型的时间戳
    with pytest.raises(TypeError):
        Waveform(data, sampling_rate=sampling_rate, timestamp="2024-01-01")
    
    print("无效时间戳验证成功")


def test_waveform_invalid_dimensions():
    """测试无效维度"""
    sampling_rate = 1000
    
    # 测试3维数组
    data_3d = np.array([[[1.0, 2.0], [3.0, 4.0]], [[5.0, 6.0], [7.0, 8.0]]])
    with pytest.raises(ValueError):
        Waveform(data_3d, sampling_rate=sampling_rate)
    
    print("无效维度验证成功")


def test_waveform_from_list():
    """测试从列表创建波形"""
    data_list = [1.0, 2.0, 3.0, 4.0]
    sampling_rate = 1000
    
    waveform = Waveform(data_list, sampling_rate=sampling_rate)
    
    assert waveform.shape == (4,)
    assert waveform.dtype == np.float64
    assert np.array_equal(waveform, np.array(data_list))
    
    print(f"从列表创建波形成功: {waveform}")


def test_waveform_numpy_operations():
    """测试numpy操作兼容性"""
    data = np.array([1.0, 2.0, 3.0, 4.0])
    sampling_rate = 1000
    
    waveform = Waveform(data, sampling_rate=sampling_rate)
    
    # 测试基本数学运算
    result = waveform * 2
    assert isinstance(result, Waveform)
    assert np.array_equal(result, data * 2)
    
    # 测试切片
    sliced = waveform[1:3]
    assert isinstance(sliced, Waveform)
    assert np.array_equal(sliced, data[1:3])
    
    print("numpy操作兼容性验证成功")


if __name__ == "__main__":
    # 运行所有测试
    test_waveform_creation_1d()
    test_waveform_creation_2d()
    test_waveform_with_timestamp()
    test_waveform_timestamp_modification()
    test_waveform_sampling_rate_readonly()
    test_waveform_invalid_sampling_rate()
    test_waveform_invalid_timestamp()
    test_waveform_invalid_dimensions()
    test_waveform_from_list()
    test_waveform_numpy_operations()
    
    print("\n所有测试通过！")
