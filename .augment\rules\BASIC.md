---
type: "always_apply"
---

<Basic_Rules>

Environments:
- We are working with Python (miniconda).
- Always check if the Virtual Environment Prompt Indicator in the terminal is “sweep”. If not, run “conda activate sweep” to enable the virtual environment. 
- Python interpreter version: 3.12.11
- Some installed packages: nidaqmx, scipy, matplotlib, numpy, pytest
- **特别注意**，如需安装新的包，请停止响应并让我手动处理，严禁自行安装。
- The "Type Checking Mode" option of "Pyright" has been set to "strict". Please comply with the relevant type checking specifications as strictly as possible. If you encounter problems that you can't solve, please stop responding and let me handle them manually.

---

Main Task:
- 我们正在开发一个"sweeper400" package，它的主要功能是：协同控制NI数据采集卡（使用现有的"nidaqmx" package）和步进电机（使用"MT_API.dll"文件），自动化分步采集空间中不同位置的信号，并对信号进行处理，获取信号的空间分布等信息。
- "sweeper400" package 包含以下子包："measure"（包含NI数据采集相关module），"move"（包含步进电机控制相关module），"analyze"（包含信号和数据处理相关module），"sweeper"（协同调用其他子包，将功能封装为适用于特定任务的专用对象，供外部以简洁的方式使用）

---

Detailed Rules:
- 本包配置了日志管理框架"sweeper400\sweeper400\logger.py"，请在开发中使用它统一进行日志管理，合理为我们的代码配置日志输出，方便你监测代码的运行情况。你可以查看该系统的详细说明文档"sweeper400\sweeper400\README_LOGGER.md"。（非必要不要修改"logger.py"。如果确需修改，不要忘了同步更新说明文档。）
- 请在开发中遵循以下方式："sweeper400" package的所有文件位于根目录的"sweeper400"目录中，测试代码则位于根目录的"tests"目录中。请在"sweeper400\sweeper400"目录中编写各子包/模块源代码（实现所有的函数/类/方法/属性），在"tests"目录中编写测试代码调用"sweeper400" package（暂未安装，你需要使用路径import）。
- 如果涉及添加只读属性，默认使用@property装饰器实现。
- 开发过程中，请不要忘记适时更新各级"__init__.py"和配置文件"sweeper400\pyproject.toml"。（更新"sweeper400\pyproject.toml"后，如需在sweep环境中更新配置，请使用"python -m pip install -e"重新安装"sweeper400"。）
- 不要修改项目根目录下的"pyproject.toml"文件，其与我们目前的工作无关。
- 除非明确要求，否则不要主动创建“使用示例/演示脚本”和“使用指南/说明文档”，只要将代码注释写得清楚详细即可。

</Basic_Rules>