"""
测试 basic_sine.init_sampling_info 函数
"""

from sweeper400.analyze import init_sampling_info
from sweeper400.analyze.my_dtypes import PositiveInt, SamplingInfo, validate_positive


def test_init_sampling_info_basic():
    """测试基本功能"""
    sampling_rate = PositiveInt(1000)
    samples_num = PositiveInt(2048)

    result = init_sampling_info(sampling_rate, samples_num)

    # 检查返回类型
    assert isinstance(result, dict)

    # 检查字典内容
    assert result["sampling_rate"] == 1000
    assert result["samples_num"] == 2048

    # 检查字典键
    assert set(result.keys()) == {"sampling_rate", "samples_num"}


def test_init_sampling_info_different_values():
    """测试不同的输入值"""
    test_cases = [
        (PositiveInt(44100), PositiveInt(1024)),
        (PositiveInt(48000), PositiveInt(4096)),
        (PositiveInt(96000), PositiveInt(8192)),
    ]

    for sampling_rate, samples_num in test_cases:
        result = init_sampling_info(sampling_rate, samples_num)

        assert result["sampling_rate"] == sampling_rate
        assert result["samples_num"] == samples_num


def test_init_sampling_info_with_validate_positive():
    """测试与 validate_positive 函数配合使用"""
    # 使用 validate_positive 创建 PositiveInt
    sampling_rate = validate_positive(22050)
    samples_num = validate_positive(512)

    result = init_sampling_info(sampling_rate, samples_num)

    assert result["sampling_rate"] == 22050
    assert result["samples_num"] == 512


def test_validate_positive_error():
    """测试 validate_positive 对非正整数的错误处理"""
    with pytest.raises(ValueError, match="PositiveInt必须是正整数"):
        validate_positive(0)

    with pytest.raises(ValueError, match="PositiveInt必须是正整数"):
        validate_positive(-1)


def run_manual_tests():
    """运行手动测试（不依赖pytest）"""
    print("=" * 50)
    print("测试 init_sampling_info 函数")
    print("=" * 50)

    # 测试1: 基本功能
    print("\n测试1: 基本功能")
    sampling_rate = PositiveInt(1000)
    samples_num = PositiveInt(2048)
    result = init_sampling_info(sampling_rate, samples_num)
    print(f"输入: sampling_rate={sampling_rate}, samples_num={samples_num}")
    print(f"结果: {result}")
    assert result["sampling_rate"] == 1000
    assert result["samples_num"] == 2048
    assert set(result.keys()) == {"sampling_rate", "samples_num"}
    print("✓ 测试1通过")

    # 测试2: 不同的输入值
    print("\n测试2: 不同的输入值")
    test_cases = [
        (PositiveInt(44100), PositiveInt(1024)),
        (PositiveInt(48000), PositiveInt(4096)),
        (PositiveInt(96000), PositiveInt(8192)),
    ]

    for i, (sr, sn) in enumerate(test_cases, 1):
        result = init_sampling_info(sr, sn)
        print(f"  测试用例{i}: sampling_rate={sr}, samples_num={sn} -> {result}")
        assert result["sampling_rate"] == sr
        assert result["samples_num"] == sn
    print("✓ 测试2通过")

    # 测试3: 与 validate_positive 配合使用
    print("\n测试3: 与 validate_positive 配合使用")
    sampling_rate3 = validate_positive(22050)
    samples_num3 = validate_positive(512)
    result3 = init_sampling_info(sampling_rate3, samples_num3)
    print(f"输入: sampling_rate={sampling_rate3}, samples_num={samples_num3}")
    print(f"结果: {result3}")
    assert result3["sampling_rate"] == 22050
    assert result3["samples_num"] == 512
    print("✓ 测试3通过")

    # 测试4: validate_positive 错误处理
    print("\n测试4: validate_positive 错误处理")
    try:
        validate_positive(0)
        assert False, "应该抛出ValueError"
    except ValueError as e:
        print(f"  正确捕获错误: {e}")

    try:
        validate_positive(-1)
        assert False, "应该抛出ValueError"
    except ValueError as e:
        print(f"  正确捕获错误: {e}")
    print("✓ 测试4通过")

    print("\n" + "=" * 50)
    print("所有测试通过！init_sampling_info 函数工作正常。")
    print("=" * 50)


if __name__ == "__main__":
    run_manual_tests()
