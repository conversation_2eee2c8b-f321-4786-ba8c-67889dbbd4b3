[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "sweeper400"
version = "0.1.0"
description = "协同控制NI数据采集卡和步进电机的自动化测量包"
authors = [
    {name = "sweeper400 team"}
]
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "numpy",
    "scipy",
    "matplotlib",
    "nidaqmx",
]

[project.optional-dependencies]
dev = [
    "pytest",
    "black",
]

[tool.setuptools.packages.find]
where = ["."]
include = ["sweeper400*"]

[tool.black]
line-length = 88
target-version = ['py312']