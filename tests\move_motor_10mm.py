"""
直接移动电机10mm的测试脚本
使用螺距4mm，细分数64（待验证）
"""

import sys
import os
import time

# 添加项目根目录到Python路径
project_root = os.path.join(os.path.dirname(__file__), "..")
sys.path.insert(0, project_root)

# 添加sweeper400包路径
sweeper400_path = os.path.join(project_root, "sweeper400")
sys.path.insert(0, sweeper400_path)

from sweeper400.move.move import MotorController
from ctypes import c_int32, pointer
import logging

# 配置日志级别
logging.basicConfig(level=logging.INFO)

def move_motor_10mm():
    """直接移动电机10mm"""
    print("开始移动电机10mm测试")
    print("参数: 螺距=4mm, 细分数=64(待验证)")
    
    controller = MotorController()
    
    try:
        # 初始化和连接
        print("初始化API...")
        controller.initialize()
        print("连接USB...")
        controller.connect_usb()
        
        # 设置参数
        controller.set_motor_parameters(1.8, 64, 4.0, 1.0)
        conversion_factor = controller.get_conversion_factor()
        print(f"换算系数: {conversion_factor:.2f} 步/mm")
        
        # 计算10mm对应的步数
        target_distance = 10.0
        steps_needed = controller.mm_to_steps(target_distance)
        print(f"10mm = {steps_needed} 步")
        
        # 设置电机参数
        axis = 0  # X轴
        controller._api.MT_Set_Axis_Halt_All()
        controller._api.MT_Set_Axis_Mode_Position(axis)
        controller._api.MT_Set_Axis_Position_Acc(axis, 2000)
        controller._api.MT_Set_Axis_Position_Dec(axis, 2000)
        controller._api.MT_Set_Axis_Position_V_Max(axis, 3000)
        
        # 获取初始位置
        current_pos = c_int32(0)
        p_pos = pointer(current_pos)
        controller._api.MT_Get_Axis_Software_P_Now(axis, p_pos)
        initial_position = current_pos.value
        print(f"初始位置: {initial_position} 步")
        
        # 开始移动
        print(f"开始移动 {steps_needed} 步...")
        controller._api.MT_Set_Axis_Position_P_Target_Rel(axis, steps_needed)
        
        # 等待移动完成
        run_status = c_int32(0)
        p_run = pointer(run_status)
        
        print("电机正在移动...")
        while True:
            controller._api.MT_Get_Axis_Status_Run(axis, p_run)
            if run_status.value == 0:  # 0表示停止
                break
            time.sleep(0.1)
        
        # 获取最终位置
        controller._api.MT_Get_Axis_Software_P_Now(axis, p_pos)
        final_position = current_pos.value
        actual_steps = final_position - initial_position
        
        print("移动完成！")
        print(f"初始位置: {initial_position} 步")
        print(f"最终位置: {final_position} 步")
        print(f"实际移动: {actual_steps} 步")
        print(f"理论移动: {steps_needed} 步")
        
        theoretical_distance = controller.steps_to_mm(actual_steps)
        print(f"理论距离: {theoretical_distance:.3f} mm")
        
        print("\n" + "="*50)
        print("请测量电机的实际移动距离！")
        print("然后告诉我实际测量值，我将分析细分数是否正确。")
        print("="*50)
        
        return True
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        controller.cleanup()

if __name__ == "__main__":
    move_motor_10mm()
