"""
测试电机参数获取和换算功能

该测试文件用于：
1. 测试电机控制器的初始化和连接
2. 获取硬件信息
3. 测试脉冲数与物理长度的换算关系
4. 验证换算系数的计算

注意：此测试需要实际的电机控制器硬件连接
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.join(os.path.dirname(__file__), "..")
sys.path.insert(0, project_root)

# 添加sweeper400包路径
sweeper400_path = os.path.join(project_root, "sweeper400")
sys.path.insert(0, sweeper400_path)

from sweeper400.move.move import MotorController
import logging

# 配置日志级别为DEBUG以查看详细信息
logging.basicConfig(level=logging.DEBUG)


def test_motor_controller():
    """测试电机控制器的基本功能"""
    print("=" * 60)
    print("开始测试电机控制器")
    print("=" * 60)

    # 创建电机控制器实例
    controller = MotorController()

    try:
        # 1. 初始化API
        print("\n1. 初始化API...")
        if controller.initialize():
            print("√ API初始化成功")
        else:
            print("× API初始化失败")
            return False

        # 2. 连接USB
        print("\n2. 连接USB...")
        if controller.connect_usb():
            print("√ USB连接成功")
        else:
            print("× USB连接失败")
            return False

        # 3. 获取硬件信息
        print("\n3. 获取硬件信息...")
        hardware_info = controller.get_hardware_info()
        if hardware_info:
            print("√ 硬件信息获取成功:")
            for key, value in hardware_info.items():
                print(f"  {key}: {value}")
        else:
            print("× 硬件信息获取失败")

        # 4. 设置电机参数（这些参数需要根据实际硬件确定）
        print("\n4. 设置电机参数...")
        print("请注意：以下参数是示例值，需要根据实际硬件调整")

        # 常见的步进电机参数
        step_angle = 1.8  # 步进角度（度）
        subdivision = 32  # 细分数（需要确认）
        pitch = 2.0  # 螺距（mm）（需要确认）
        transmission_ratio = 1.0  # 传动比

        controller.set_motor_parameters(step_angle, subdivision, pitch, transmission_ratio)
        print("√ 电机参数设置完成")

        # 5. 显示当前参数
        print("\n5. 当前电机参数:")
        params = controller.get_motor_parameters()
        for key, value in params.items():
            print(f"  {key}: {value}")

        # 6. 计算换算系数
        print("\n6. 计算换算系数...")
        try:
            conversion_factor = controller.get_conversion_factor()
            print(f"√ 换算系数: {conversion_factor:.2f} 步/mm")
        except Exception as e:
            print(f"× 换算系数计算失败: {e}")

        # 7. 测试单位换算
        print("\n7. 测试单位换算...")
        test_distances = [1.0, 5.0, 10.0, 0.1]  # mm

        for distance in test_distances:
            try:
                steps = controller.mm_to_steps(distance)
                back_to_mm = controller.steps_to_mm(steps)
                print(f"  {distance}mm -> {steps}步 -> {back_to_mm:.3f}mm")
            except Exception as e:
                print(f"  换算测试失败 ({distance}mm): {e}")

        print("\n" + "=" * 60)
        print("测试完成！")
        print("=" * 60)

        return True

    except Exception as e:
        print(f"\n× 测试过程中发生异常: {e}")
        return False

    finally:
        # 清理资源
        print("\n清理资源...")
        controller.cleanup()
        print("√ 资源清理完成")


def get_unknown_parameters():
    """获取需要手动确定的参数"""
    print("\n" + "=" * 60)
    print("需要手动确定的参数")
    print("=" * 60)

    print(
        """
根据API文档，脉冲数与物理长度的换算需要以下参数：

1. 步进角度 (AStepAngle): 
   - 通常为1.8°（200步/圈）或0.9°（400步/圈）
   - 可以通过电机铭牌或规格书确认

2. 驱动器细分数 (ADiv):
   - 常见值：2, 4, 8, 16, 32, 64, 128等
   - 需要查看驱动器设置或配置

3. 螺距 (APitch):
   - 电机旋转一圈时直线台移动的距离（mm）
   - 需要查看直线台规格或实际测量

4. 传动比 (ALineRatio):
   - 如果有减速器或传动机构，需要确认传动比
   - 无传动时为1.0

请手动确认这些参数后，在代码中设置正确的值。
    """
    )


if __name__ == "__main__":
    print("电机参数获取和换算测试")
    print("请确保电机控制器已正确连接到PC")

    # 显示需要确认的参数
    get_unknown_parameters()

    # 询问是否继续测试
    response = input("\n是否继续进行硬件测试？(y/n): ").lower().strip()

    if response == "y" or response == "yes":
        success = test_motor_controller()
        if success:
            print("\n测试成功完成！")
        else:
            print("\n测试失败，请检查硬件连接和参数设置。")
    else:
        print("测试已取消。")
